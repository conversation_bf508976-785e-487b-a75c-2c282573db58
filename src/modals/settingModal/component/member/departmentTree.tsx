import React, { useState, useRef } from 'react';
import { Input, Dropdown, Modal, type InputRef } from 'antd';
import { PlusOutlined, MoreOutlined, EditOutlined, DeleteOutlined, TeamOutlined, ApartmentOutlined } from '@ant-design/icons';
import { cn } from '@/utils/utils';

interface DepartmentNode {
  id: string;
  name: string;
  children?: DepartmentNode[];
  isEditing?: boolean;
}

interface DragState {
  draggedNodeId: string | null;
  dragOverNodeId: string | null;
  dropPosition: 'top' | 'middle' | 'bottom' | null;
}

interface DepartmentTreeProps {
  className?: string;
}

export default function DepartmentTree({ className }: DepartmentTreeProps) {
  const [treeData, setTreeData] = useState<DepartmentNode>({
    id: 'root',
    name: 'AIWorks 团队',
    children: [
      {
        id: 'tech',
        name: '技术部',
        children: [
          { id: 'frontend', name: '前端小组' },
          { id: 'backend', name: '后端小组' },
          { id: 'test', name: '测试小组' }
        ]
      },
      {
        id: 'product',
        name: '产品部',
        children: [
          { id: 'design', name: '设计小组' },
          { id: 'pm', name: '产品经理小组' }
        ]
      },
      {
        id: 'market',
        name: '市场部',
        children: [
          { id: 'operation', name: '运营小组' }
        ]
      }
    ]
  });

  const [expandedKeys, setExpandedKeys] = useState<Set<string>>(new Set(['tech', 'product', 'market']));
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [nodeToDelete, setNodeToDelete] = useState<string | null>(null);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [dragState, setDragState] = useState<DragState>({
    draggedNodeId: null,
    dragOverNodeId: null,
    dropPosition: null
  });
  const inputRef = useRef<InputRef>(null);

  // 更新节点
  const updateNode = (nodeId: string, updates: Partial<DepartmentNode>) => {
    const updateNodeRecursive = (node: DepartmentNode): DepartmentNode => {
      if (node.id === nodeId) {
        return { ...node, ...updates };
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(updateNodeRecursive)
        };
      }
      return node;
    };
    setTreeData(updateNodeRecursive(treeData));
  };

  // 添加子节点
  const addChildNode = (parentId: string) => {
    const newNodeId = `new_${Date.now()}`;
    const newNode: DepartmentNode = {
      id: newNodeId,
      name: '',
      isEditing: true
    };

    const addNodeRecursive = (node: DepartmentNode): DepartmentNode => {
      if (node.id === parentId) {
        return {
          ...node,
          children: [...(node.children || []), newNode]
        };
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(addNodeRecursive)
        };
      }
      return node;
    };

    setTreeData(addNodeRecursive(treeData));
    setExpandedKeys(prev => new Set(Array.from(prev).concat(parentId)));

    // 延迟聚焦到输入框
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 100);
  };

  // 删除节点
  const deleteNode = (nodeId: string) => {
    const deleteNodeRecursive = (node: DepartmentNode): DepartmentNode => {
      if (node.children) {
        return {
          ...node,
          children: node.children.filter(child => child.id !== nodeId).map(deleteNodeRecursive)
        };
      }
      return node;
    };
    setTreeData(deleteNodeRecursive(treeData));
    setDeleteModalVisible(false);
    setNodeToDelete(null);
  };

  // 开始编辑
  const startEdit = (nodeId: string) => {
    updateNode(nodeId, { isEditing: true });
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 100);
  };

  // 保存编辑
  const saveEdit = (nodeId: string, newName: string) => {
    if (newName.trim()) {
      updateNode(nodeId, { name: newName.trim(), isEditing: false });
    } else {
      // 如果是新节点且名称为空，删除该节点
      if (nodeId.startsWith('new_')) {
        deleteNode(nodeId);
      } else {
        updateNode(nodeId, { isEditing: false });
      }
    }
  };

  // 取消编辑
  const cancelEdit = (nodeId: string) => {
    if (nodeId.startsWith('new_')) {
      deleteNode(nodeId);
    } else {
      updateNode(nodeId, { isEditing: false });
    }
  };

  // 切换展开状态
  const toggleExpanded = (nodeId: string) => {
    setExpandedKeys(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  // 查找节点及其父节点
  const findNodeWithParent = (nodeId: string, node: DepartmentNode = treeData, parent: DepartmentNode | null = null): { node: DepartmentNode; parent: DepartmentNode | null } | null => {
    if (node.id === nodeId) {
      return { node, parent };
    }
    if (node.children) {
      for (const child of node.children) {
        const result = findNodeWithParent(nodeId, child, node);
        if (result) return result;
      }
    }
    return null;
  };

  // 移动节点
  const moveNode = (draggedNodeId: string, targetNodeId: string, position: 'top' | 'middle' | 'bottom') => {
    const draggedResult = findNodeWithParent(draggedNodeId);
    const targetResult = findNodeWithParent(targetNodeId);

    if (!draggedResult || !targetResult) return;

    const { node: draggedNode } = draggedResult;

    // 不能拖拽到自己或自己的子节点
    if (draggedNodeId === targetNodeId || isDescendant(draggedNode, targetNodeId)) {
      return;
    }

    // 从原位置移除
    const removeFromParent = (parentNode: DepartmentNode, childId: string) => {
      if (parentNode.children) {
        parentNode.children = parentNode.children.filter(child => child.id !== childId);
      }
    };

    // 添加到新位置
    const addToParent = (parentNode: DepartmentNode, childNode: DepartmentNode, index?: number) => {
      if (!parentNode.children) {
        parentNode.children = [];
      }
      if (index !== undefined) {
        parentNode.children.splice(index, 0, childNode);
      } else {
        parentNode.children.push(childNode);
      }
    };

    setTreeData(prevTreeData => {
      const newTreeData = JSON.parse(JSON.stringify(prevTreeData));

      // 重新查找节点（因为是新的对象）
      const newDraggedResult = findNodeWithParent(draggedNodeId, newTreeData);
      const newTargetResult = findNodeWithParent(targetNodeId, newTreeData);

      if (!newDraggedResult || !newTargetResult) return prevTreeData;

      const { node: newDraggedNode, parent: newDraggedParent } = newDraggedResult;
      const { node: newTargetNode, parent: newTargetParent } = newTargetResult;

      // 从原位置移除
      if (newDraggedParent) {
        removeFromParent(newDraggedParent, draggedNodeId);
      }

      if (position === 'middle') {
        // 放到目标节点的子级
        addToParent(newTargetNode, newDraggedNode);
        // 展开目标节点
        setExpandedKeys(prev => new Set(Array.from(prev).concat(targetNodeId)));
      } else {
        // 放到目标节点的同级
        if (newTargetParent) {
          const targetIndex = newTargetParent.children?.findIndex(child => child.id === targetNodeId) ?? -1;
          if (targetIndex !== -1) {
            const insertIndex = position === 'top' ? targetIndex : targetIndex + 1;
            addToParent(newTargetParent, newDraggedNode, insertIndex);
          }
        }
      }

      return newTreeData;
    });
  };

  // 检查是否为后代节点
  const isDescendant = (ancestorNode: DepartmentNode, nodeId: string): boolean => {
    if (!ancestorNode.children) return false;

    for (const child of ancestorNode.children) {
      if (child.id === nodeId || isDescendant(child, nodeId)) {
        return true;
      }
    }
    return false;
  };

  // 拖拽事件处理
  const handleDragStart = (e: React.DragEvent, nodeId: string) => {
    if (nodeId === 'root') {
      e.preventDefault();
      return;
    }
    setDragState(prev => ({ ...prev, draggedNodeId: nodeId }));
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, nodeId: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';

    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const y = e.clientY - rect.top;
    const height = rect.height;

    let position: 'top' | 'middle' | 'bottom';
    if (y < height * 0.25) {
      position = 'top';
    } else if (y > height * 0.75) {
      position = 'bottom';
    } else {
      position = 'middle';
    }

    setDragState(prev => ({
      ...prev,
      dragOverNodeId: nodeId,
      dropPosition: position
    }));
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // 只有当鼠标真正离开节点区域时才清除状态
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDragState(prev => ({
        ...prev,
        dragOverNodeId: null,
        dropPosition: null
      }));
    }
  };

  const handleDrop = (e: React.DragEvent, nodeId: string) => {
    e.preventDefault();

    if (dragState.draggedNodeId && dragState.dropPosition) {
      moveNode(dragState.draggedNodeId, nodeId, dragState.dropPosition);
    }

    setDragState({
      draggedNodeId: null,
      dragOverNodeId: null,
      dropPosition: null
    });
  };

  const handleDragEnd = () => {
    setDragState({
      draggedNodeId: null,
      dragOverNodeId: null,
      dropPosition: null
    });
  };

  // 节点选中处理
  const handleNodeClick = (nodeId: string) => {
    setSelectedNodeId(nodeId);
  };

  // 渲染节点
  const renderNode = (node: DepartmentNode, level = 0): React.ReactNode => {
    const isExpanded = expandedKeys.has(node.id);
    const hasChildren = node.children && node.children.length > 0;
    const isRoot = level === 0;
    const isSelected = selectedNodeId === node.id;
    const isDragging = dragState.draggedNodeId === node.id;
    const isDragOver = dragState.dragOverNodeId === node.id;
    const dropPosition = isDragOver ? dragState.dropPosition : null;

    const menuItems = [
      {
        key: 'rename',
        label: (
          <div className="flex items-center gap-2">
            <EditOutlined className="text-sm" />
            重命名
          </div>
        ),
        onClick: () => startEdit(node.id)
      },
      {
        key: 'delete',
        label: (
          <div className="flex items-center gap-2 text-red-500">
            <DeleteOutlined className="text-sm" />
            删除
          </div>
        ),
        onClick: () => {
          setNodeToDelete(node.id);
          setDeleteModalVisible(true);
        }
      }
    ];

    return (
      <div key={node.id}>
        {/* 拖拽指示器 */}
        {isDragOver && dropPosition === 'top' && (
          <div className="h-0.5 bg-blue-500 mx-2 mb-1"></div>
        )}

        {/* 节点内容 */}
        <div
          draggable={!isRoot}
          className={cn(
            "group flex items-center justify-between py-2 transition-all duration-200 relative w-full cursor-pointer",
            isRoot
              ? "bg-blue-50 rounded-lg px-3 mb-2"
              : "hover:bg-gray-50 px-2 rounded-md",
            isSelected && "bg-blue-100 border border-blue-300",
            isDragging && "opacity-50",
            isDragOver && dropPosition === 'middle' && "bg-blue-50 border border-blue-300"
          )}
          style={{
            paddingLeft: isRoot ? 12 : 12 + level * 24,
            minHeight: '36px'
          }}
          onMouseEnter={() => setHoveredNode(node.id)}
          onMouseLeave={() => setHoveredNode(null)}
          onClick={() => handleNodeClick(node.id)}
          onDragStart={(e) => handleDragStart(e, node.id)}
          onDragOver={(e) => handleDragOver(e, node.id)}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, node.id)}
          onDragEnd={handleDragEnd}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {/* 展开/收起图标 */}
            {hasChildren && !isRoot && (
              <button
                onClick={() => toggleExpanded(node.id)}
                className="flex-shrink-0 w-4 h-4 flex items-center justify-center text-gray-500 hover:text-gray-700 mr-1"
              >
                <span className={cn("transition-transform text-xs", isExpanded ? "rotate-90" : "")}>
                  ▶
                </span>
              </button>
            )}
            {/* 根节点或没有子节点时的占位 */}
            {(!hasChildren || isRoot) && !isRoot && (
              <div className="w-4 h-4 mr-1"></div>
            )}

            {/* 图标 */}
            <div className="flex-shrink-0">
              {isRoot ? (
                <ApartmentOutlined className="text-blue-500 text-base" />
              ) : (
                <TeamOutlined className="text-blue-500 text-base" />
              )}
            </div>

            {/* 名称或编辑框 */}
            <div className="flex-1 min-w-0">
              {node.isEditing ? (
                <Input
                  ref={inputRef}
                  defaultValue={node.name}
                  placeholder="组织名称"
                  className="h-7 text-sm"
                  onPressEnter={(e) => {
                    const target = e.target as HTMLInputElement;
                    saveEdit(node.id, target.value);
                  }}
                  onBlur={(e) => {
                    saveEdit(node.id, e.target.value);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Escape') {
                      cancelEdit(node.id);
                    }
                  }}
                />
              ) : (
                <span className={cn(
                  "text-sm truncate block",
                  isRoot ? "font-medium text-gray-900" : "text-gray-700"
                )}>
                  {node.name}
                </span>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          {!node.isEditing && !isRoot && hoveredNode === node.id && (
            <div className="flex items-center gap-1 flex-shrink-0 opacity-100">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  addChildNode(node.id);
                }}
                className="w-7 h-7 flex items-center justify-center text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded transition-all duration-200"
                title="添加"
              >
                <PlusOutlined className="text-sm" />
              </button>

              <Dropdown
                menu={{ items: menuItems }}
                trigger={['click']}
                placement="bottomRight"
                getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
              >
                <button
                  onClick={(e) => e.stopPropagation()}
                  className="w-7 h-7 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-all duration-200"
                  title="更多"
                >
                  <MoreOutlined className="text-sm" />
                </button>
              </Dropdown>
            </div>
          )}
        </div>

        {/* 子节点 */}
        {hasChildren && (isExpanded || isRoot) && (
          <div>
            {node.children?.map(child => renderNode(child, level + 1))}
          </div>
        )}

        {/* 底部拖拽指示器 */}
        {isDragOver && dropPosition === 'bottom' && (
          <div className="h-0.5 bg-blue-500 mx-2 mt-1"></div>
        )}
      </div>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      {/* 标题 */}
      <h3 className="text-lg font-medium text-gray-900 mb-4">组织架构</h3>

      {/* 树形结构 */}
      <div className="space-y-1">
        {renderNode(treeData)}
      </div>

      {/* 删除确认弹窗 */}
      <Modal
        title="删除确认"
        open={deleteModalVisible}
        onOk={() => nodeToDelete && deleteNode(nodeToDelete)}
        onCancel={() => {
          setDeleteModalVisible(false);
          setNodeToDelete(null);
        }}
        okText="确认删除"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <p>确定要删除该组织吗？删除后不可恢复。</p>
      </Modal>
    </div>
  );
}
