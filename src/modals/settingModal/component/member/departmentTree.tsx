import React, { useState, useRef } from 'react';
import { Input, Dropdown, Modal, type InputRef } from 'antd';
import { PlusOutlined, MoreOutlined, EditOutlined, DeleteOutlined, TeamOutlined, ApartmentOutlined } from '@ant-design/icons';
import { cn } from '@/utils/utils';

interface DepartmentNode {
  id: string;
  name: string;
  children?: DepartmentNode[];
  isEditing?: boolean;
}

interface DepartmentTreeProps {
  className?: string;
}

export default function DepartmentTree({ className }: DepartmentTreeProps) {
  const [treeData, setTreeData] = useState<DepartmentNode>({
    id: 'root',
    name: 'AIWorks 团队',
    children: [
      {
        id: 'tech',
        name: '技术部',
        children: [
          { id: 'frontend', name: '前端小组' },
          { id: 'backend', name: '后端小组' },
          { id: 'test', name: '测试小组' }
        ]
      },
      {
        id: 'product',
        name: '产品部',
        children: [
          { id: 'design', name: '设计小组' },
          { id: 'pm', name: '产品经理小组' }
        ]
      },
      {
        id: 'market',
        name: '市场部',
        children: [
          { id: 'operation', name: '运营小组' }
        ]
      }
    ]
  });

  const [expandedKeys, setExpandedKeys] = useState<Set<string>>(new Set(['tech', 'product', 'market']));
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [nodeToDelete, setNodeToDelete] = useState<string | null>(null);
  const inputRef = useRef<InputRef>(null);

  // 更新节点
  const updateNode = (nodeId: string, updates: Partial<DepartmentNode>) => {
    const updateNodeRecursive = (node: DepartmentNode): DepartmentNode => {
      if (node.id === nodeId) {
        return { ...node, ...updates };
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(updateNodeRecursive)
        };
      }
      return node;
    };
    setTreeData(updateNodeRecursive(treeData));
  };

  // 添加子节点
  const addChildNode = (parentId: string) => {
    const newNodeId = `new_${Date.now()}`;
    const newNode: DepartmentNode = {
      id: newNodeId,
      name: '',
      isEditing: true
    };

    const addNodeRecursive = (node: DepartmentNode): DepartmentNode => {
      if (node.id === parentId) {
        return {
          ...node,
          children: [...(node.children || []), newNode]
        };
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(addNodeRecursive)
        };
      }
      return node;
    };

    setTreeData(addNodeRecursive(treeData));
    setExpandedKeys(prev => new Set(Array.from(prev).concat(parentId)));

    // 延迟聚焦到输入框
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 100);
  };

  // 删除节点
  const deleteNode = (nodeId: string) => {
    const deleteNodeRecursive = (node: DepartmentNode): DepartmentNode => {
      if (node.children) {
        return {
          ...node,
          children: node.children.filter(child => child.id !== nodeId).map(deleteNodeRecursive)
        };
      }
      return node;
    };
    setTreeData(deleteNodeRecursive(treeData));
    setDeleteModalVisible(false);
    setNodeToDelete(null);
  };

  // 开始编辑
  const startEdit = (nodeId: string) => {
    updateNode(nodeId, { isEditing: true });
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 100);
  };

  // 保存编辑
  const saveEdit = (nodeId: string, newName: string) => {
    if (newName.trim()) {
      updateNode(nodeId, { name: newName.trim(), isEditing: false });
    } else {
      // 如果是新节点且名称为空，删除该节点
      if (nodeId.startsWith('new_')) {
        deleteNode(nodeId);
      } else {
        updateNode(nodeId, { isEditing: false });
      }
    }
  };

  // 取消编辑
  const cancelEdit = (nodeId: string) => {
    if (nodeId.startsWith('new_')) {
      deleteNode(nodeId);
    } else {
      updateNode(nodeId, { isEditing: false });
    }
  };

  // 切换展开状态
  const toggleExpanded = (nodeId: string) => {
    setExpandedKeys(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  // 渲染节点
  const renderNode = (node: DepartmentNode, level = 0): React.ReactNode => {
    const isExpanded = expandedKeys.has(node.id);
    const hasChildren = node.children && node.children.length > 0;
    const isHovered = hoveredNode === node.id;
    const isRoot = level === 0;

    const menuItems = [
      {
        key: 'rename',
        label: (
          <div className="flex items-center gap-2">
            <EditOutlined className="text-sm" />
            重命名
          </div>
        ),
        onClick: () => startEdit(node.id)
      },
      {
        key: 'delete',
        label: (
          <div className="flex items-center gap-2 text-red-500">
            <DeleteOutlined className="text-sm" />
            删除
          </div>
        ),
        onClick: () => {
          setNodeToDelete(node.id);
          setDeleteModalVisible(true);
        }
      }
    ];

    return (
      <div key={node.id}>
        {/* 节点内容 */}
        <div
          className={cn(
            "group flex items-center justify-between rounded-md px-3 py-2 transition-colors",
            isRoot
              ? "bg-blue-50 border border-blue-100 mb-2"
              : "hover:bg-gray-50",
            level > 0 && "ml-6"
          )}
          style={{ paddingLeft: isRoot ? 12 : 12 + (level - 1) * 24 }}
          onMouseEnter={() => setHoveredNode(node.id)}
          onMouseLeave={() => setHoveredNode(null)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {/* 展开/收起图标 */}
            {hasChildren && !isRoot && (
              <button
                onClick={() => toggleExpanded(node.id)}
                className="flex-shrink-0 w-4 h-4 flex items-center justify-center text-gray-400 hover:text-gray-600"
              >
                <span className={cn("transition-transform", isExpanded ? "rotate-90" : "")}>
                  ▶
                </span>
              </button>
            )}

            {/* 图标 */}
            <div className="flex-shrink-0">
              {isRoot ? (
                <ApartmentOutlined className="text-blue-500 text-base" />
              ) : (
                <TeamOutlined className="text-blue-500 text-base" />
              )}
            </div>

            {/* 名称或编辑框 */}
            <div className="flex-1 min-w-0">
              {node.isEditing ? (
                <Input
                  ref={inputRef}
                  defaultValue={node.name}
                  placeholder="组织名称"
                  className="h-7 text-sm"
                  onPressEnter={(e) => {
                    const target = e.target as HTMLInputElement;
                    saveEdit(node.id, target.value);
                  }}
                  onBlur={(e) => {
                    saveEdit(node.id, e.target.value);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Escape') {
                      cancelEdit(node.id);
                    }
                  }}
                />
              ) : (
                <span className={cn(
                  "text-sm truncate block",
                  isRoot ? "font-medium text-gray-900" : "text-gray-700"
                )}>
                  {node.name}
                </span>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          {!node.isEditing && !isRoot && (isHovered || hoveredNode === node.id) && (
            <div className="flex items-center gap-1 flex-shrink-0">
              <button
                onClick={() => addChildNode(node.id)}
                className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded transition-colors"
                title="添加"
              >
                <PlusOutlined className="text-xs" />
              </button>

              <Dropdown
                menu={{ items: menuItems }}
                trigger={['click']}
                placement="bottomRight"
              >
                <button
                  className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
                  title="更多"
                >
                  <MoreOutlined className="text-xs" />
                </button>
              </Dropdown>
            </div>
          )}
        </div>

        {/* 子节点 */}
        {hasChildren && (isExpanded || isRoot) && (
          <div className={cn(isRoot ? "" : "ml-4")}>
            {node.children?.map(child => renderNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      {/* 标题 */}
      <h3 className="text-lg font-medium text-gray-900 mb-4">组织架构</h3>

      {/* 树形结构 */}
      <div className="space-y-1">
        {renderNode(treeData)}
      </div>

      {/* 删除确认弹窗 */}
      <Modal
        title="删除确认"
        open={deleteModalVisible}
        onOk={() => nodeToDelete && deleteNode(nodeToDelete)}
        onCancel={() => {
          setDeleteModalVisible(false);
          setNodeToDelete(null);
        }}
        okText="确认删除"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <p>确定要删除该组织吗？删除后不可恢复。</p>
      </Modal>
    </div>
  );
}
