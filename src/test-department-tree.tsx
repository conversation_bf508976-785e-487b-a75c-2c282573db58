import React from 'react';
import DepartmentTree from './modals/settingModal/component/member/departmentTree';
import 'antd/dist/reset.css';

function TestDepartmentTree() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
        <DepartmentTree />
      </div>

      {/* 功能说明 */}
      <div className="max-w-2xl mx-auto mt-8 bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold mb-4">组织架构目录树功能说明</h2>
        <div className="space-y-3 text-sm text-gray-600">
          <div>
            <strong>✅ 已实现功能：</strong>
          </div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>顶部展示一级组织名称和图标（AIWorks 团队）</li>
            <li>多层级子组织结构，支持展开和收起</li>
            <li>鼠标hover时显示"添加"和"更多"操作按钮</li>
            <li>点击"添加"按钮可在当前节点下添加子节点</li>
            <li>新增节点自动进入编辑状态，支持键盘操作：
              <ul className="list-disc list-inside ml-4 mt-1">
                <li>回车键：保存</li>
                <li>ESC键：取消</li>
                <li>失去焦点：保存</li>
              </ul>
            </li>
            <li>点击"更多"按钮显示下拉菜单（重命名、删除）</li>
            <li>重命名功能：点击后节点变为输入框状态</li>
            <li>删除功能：弹出确认弹窗</li>
            <li>节点长度撑满右侧内容</li>
            <li>严格按照设计稿样式实现</li>
          </ul>

          <div className="mt-4">
            <strong>🎨 样式特点：</strong>
          </div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>一级组织：蓝色背景，圆角边框</li>
            <li>子组织：hover时灰色背景</li>
            <li>图标：一级组织使用部门图标，子组织使用团队图标</li>
            <li>操作按钮：hover时显示，支持添加和更多操作</li>
            <li>层级缩进：清晰的视觉层次</li>
          </ul>

          <div className="mt-4">
            <strong>🔧 技术实现：</strong>
          </div>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>使用 React + TypeScript</li>
            <li>使用 Ant Design 组件库（Input、Dropdown、Modal）</li>
            <li>使用 Tailwind CSS 进行样式设计</li>
            <li>完全自定义的树形结构实现</li>
            <li>支持键盘交互和鼠标操作</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

export default TestDepartmentTree;
